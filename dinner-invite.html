<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pyxi Dinner Invite</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Permanent+Marker&family=Work+Sans:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <script src="./js/config.js"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ["Work Sans", "sans-serif"],
              marker: ["Permanent Marker", "cursive"],
            },
            colors: { "pyxi-red": "#F09E5B" },
          },
        },
      };
    </script>
  </head>
  <body class="font-sans bg-gray-900 text-white">
    <!-- Hero -->
    <header class="relative">
      <div class="relative mx-auto w-full max-w-[480px] h-[300px]">
        <img src="./images/dinner-invive.png" alt="DinnerInvite" class="absolute inset-0 w-full h-full object-cover" loading="lazy" />
        <div class="absolute inset-0 bg-black/60"></div>
        <div class="absolute inset-0 z-10 px-4 pt-4">
          <img src="./images/pyxi-logo.png" alt="Pyxi" class="h-6 w-auto" />
          <h1 class="mt-3 text-left text-3xl font-semibold leading-tight">
            <span id="inviterName"></span></span> is inviting you to a Pyxi<br />dinner experience
          </h1>
        </div>
      </div>
    </header>

    <!-- Body copy card -->
    <main class="mx-auto w-full max-w-[480px] px-4">
      <section
        class="mt-4"
      >
        <p class="text-gray-200">Hey,</p>
        <p class="text-gray-300 mt-4">
          We’ve got good news — <span id="inviterName1"></span> just reserved a spot for you at
          one of our curated Pyxi experiences, the Reset Table!
        </p>

        <h2 class="font-semibold text-white mt-8">What is the Reset Table?</h2>
        <p class="text-gray-300 mt-2">
          It’s part of Pyxi Select, our small‑group dining series where
          strangers become good company. Each table is thoughtfully matched to
          spark real connection — and your friend thinks you’d enjoy this one.
        </p>

        <p class="text-gray-300 mt-6">
          You’re invited to join a hand‑picked group of 4 other guests for an
          evening of conversation, food, and discovery. Don’t worry,<span id="inviterName2"></span> will be there too!
        </p>

        <div class="mt-6 grid gap-3">
          <div class="flex items-center gap-3 text-gray-200">
            <span
              class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-white/10"
              >📅</span
            >
            <span class="text-sm" id="eventDate">Date: </span>
          </div>
          <div class="flex items-center gap-3 text-gray-200">
            <span
              class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-white/10"
              >📍</span
            >
            <span class="text-sm" id="eventLocation"
              >Location: </span
            >
          </div>
        </div>

        <div class="my-6 h-px bg-white/10"></div>

        <h3 class="font-semibold text-white">What to do next</h3>
        <p class="text-gray-300 mt-2">
          To join the dinner, let us know if you are already a Pyxi member, or
          joining for the first time.
        </p>

        <div class="mt-3 grid grid-cols-2 gap-3">
          <a
            href="user-check.html?mode=existing"
            class="text-center rounded-md px-4 py-3 bg-[#F09E5B] text-gray-900 font-semibold hover:opacity-90"
            >Pyxi member</a
          >
          <a
            href="user-check.html?mode=new"
            class="text-center rounded-md px-4 py-3 bg-white/5 text-white border border-white/10 font-semibold hover:bg-white/10"
            >New to Pyxi</a
          >
        </div>
        <div
          class="mt-3 rounded-md border border-white/10 bg-white/5 px-3 py-2"
        >
          <p class="text-[11px] text-gray-300">
            This helps us tailor your group and make the evening as enjoyable as
            possible.
          </p>
        </div>

        <div class="my-6 h-px bg-white/10"></div>

        <p class="text-gray-300 text-sm">
          Please confirm your attendance by <span id="confirmationDeadline"></span>.
        </p>
        <p class="text-amber-300 text-sm mt-2">
          Your seat will be released if we don’t hear from you by then. Both you
          and <span id="inviterName3"></span> will get a reminder for this.
        </p>

        <p class="text-gray-300 mt-6">
          We can’t wait to welcome you — and hope this is just the beginning of
          your Pyxi journey.
        </p>

        <div class="mt-8">
          <p class="text-gray-300">Warmly,</p>
          <p class="text-gray-300 text-sm">
            Team Pyxi <span class="ml-1">🧡</span>
          </p>
          <div class="mt-2 flex items-center justify-between">
            <p class="text-[11px] text-gray-400">
              Team Pyxi <span class="ml-1"></span>
            </p>
            <a
              class="text-xs text-[#F09E5B] hover:underline"
              href="https://www.pyxi.ai"
              target="_blank"
              rel="noreferrer"
              >www.pyxi.ai</a
            >
          </div>
        </div>
      </section>

      <footer class="py-10 text-center text-gray-400 text-xs">
        <p>
          Questions?
          <a
            class="text-[#F09E5B] hover:underline"
            href="mailto:<EMAIL>"
            ><EMAIL></a
          >
        </p>
      </footer>
    </main>
    <script>
      // Function to get parameters from URL
      function getUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        return {
          orderId: urlParams.get('order_id') || '383bcbb8-a0f4-4f53-8031-7e25397e0dd9', // Default for testing
          guestName: urlParams.get('guest_name') || 'Guest'
        };
      }

      // Function to format date
      function formatDate(dateString) {
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}.${month}.${year}`;
      }

      // Function to format time
      function formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
      }

      // Function to fetch order data and populate the page
      async function loadOrderData() {
        try {
          const params = getUrlParams();
          const orderId = params.orderId;
          const response = await fetch(`${PyxiConfig.API.BASE_URL}${PyxiConfig.ENDPOINTS.ORDERS_PUBLIC}/${orderId}`, {
            headers: {
              'accept': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          console.log('API Response:', data); // Debug log

          // Update inviter name in header
          const inviterNameElement = document.getElementById('inviterName');
          if (inviterNameElement) {
            inviterNameElement.textContent = data.user.first_name;
            console.log('Updated inviter name to:', data.user.first_name); // Debug log
          }

          // Update event date
          const eventDateElement = document.getElementById('eventDate');
          if (eventDateElement) {
            const formattedDate = formatDate(data.event.start_date);
            const startTime = formatTime(data.event.start_date);
            const endTime = formatTime(data.event.end_date);
            const dateText = `Date: ${formattedDate} (${startTime} - ${endTime})`;
            eventDateElement.textContent = dateText;
            console.log('Updated event date to:', dateText); // Debug log
          }

          // Update confirmation deadline (48 hours before event)
          const confirmationDeadlineElement = document.getElementById('confirmationDeadline');
          if (confirmationDeadlineElement) {
            const eventStartDate = new Date(data.event.start_date);
            const deadlineDate = new Date(eventStartDate.getTime() - (48 * 60 * 60 * 1000)); // 48 hours before

            const formattedDeadlineDate = formatDate(deadlineDate.toISOString());
            const deadlineTime = formatTime(deadlineDate.toISOString());
            const deadlineText = `${formattedDeadlineDate} at ${deadlineTime}`;

            confirmationDeadlineElement.textContent = deadlineText;
            console.log('Updated confirmation deadline to:', deadlineText);
          }



          // Update user names using direct ID targeting
          const fullName = `${data.user.first_name} ${data.user.last_name}`;
          console.log('Full name to replace:', fullName);

          // Update all inviter name spans by ID
          const inviterName1 = document.getElementById('inviterName1');
          if (inviterName1) {
            inviterName1.textContent =  ' ' + fullName;
            console.log('Updated inviterName1 with:', fullName);
          }

          const inviterName2 = document.getElementById('inviterName2');
          if (inviterName2) {
            inviterName2.textContent = ' ' + fullName;
            console.log('Updated inviterName2 with:', fullName);
          }

          const inviterName3 = document.getElementById('inviterName3');
          if (inviterName3) {
            inviterName3.textContent =  ' ' + fullName;
            console.log('Updated inviterName3 with:', fullName);
          }

          // Update guest name if there's an element for it
          const guestNameElement = document.getElementById('guestName');
          if (guestNameElement) {
            guestNameElement.textContent = params.guestName;
            console.log('Updated guest name with:', params.guestName);
          }

          // Replace event names in text content
          function replaceTextInElement(element, searchText, replaceText) {
            const walker = document.createTreeWalker(
              element,
              NodeFilter.SHOW_TEXT,
              null,
              false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
              textNodes.push(node);
            }

            textNodes.forEach(textNode => {
              if (textNode.textContent.includes(searchText)) {
                const oldText = textNode.textContent;
                textNode.textContent = textNode.textContent.replace(new RegExp(searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replaceText);
                console.log(`Replaced "${searchText}" with "${replaceText}" in text node: "${oldText}" -> "${textNode.textContent}"`);
              }
            });
          }

          // Replace event names throughout the document
          const mainElement = document.querySelector('main');
          const headerElement = document.querySelector('header');

          if (mainElement) {
            replaceTextInElement(mainElement, 'the Reset Table', data.event.name);
            replaceTextInElement(mainElement, 'The Reset Table', data.event.name);
          }

          if (headerElement) {
            replaceTextInElement(headerElement, 'the Reset Table', data.event.name);
            replaceTextInElement(headerElement, 'The Reset Table', data.event.name);
          }

          // Update location with event address_name
          const eventLocationElement = document.getElementById('eventLocation');
          if (eventLocationElement) {
            // Check if there's a location field in the API response
            if (data.event.location) {
              eventLocationElement.textContent = `Location: ${data.event.location}`;
              console.log('Updated location from API:', data.event.location);
            } else if (data.event.address_name) {
              // Fallback to using address_name as location info
              eventLocationElement.textContent = `Event: ${data.event.address_name}`;
              console.log('Updated location with address_name:', data.event.address_name);
            }
          }

        } catch (error) {
          console.error('Error fetching order data:', error);
          // Keep default values if API fails
        }
      }

      // Function to re-attach any event listeners after innerHTML update
      function attachEventListeners() {
        // Add any event listeners here if needed
      }

      // Load data when page loads
      document.addEventListener('DOMContentLoaded', loadOrderData);
    </script>
  </body>
</html>
