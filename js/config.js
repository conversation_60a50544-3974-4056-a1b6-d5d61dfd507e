// Pyxi Configuration
const PyxiConfig = {
  // API Base URLs
  API: {
    BASE_URL: 'https://pyxi-image-qa-953737634109.europe-west2.run.app',
    // BASE_URL: 'https://pyxi-v2-stable-dn66uttsza-nw.a.run.app',
    SIGNUP_URL: 'https://signup.pyxi.ai'
  },

  // API Endpoints
  ENDPOINTS: {
    CHECK_EMAIL_EXISTS: '/public/exists',
    ORDERS_PUBLIC: '/orders/public',
    PLUS_ONE_SHARE_LINK: '/orders/pyxi-select/plus-one/share-link'
  }
};

// Tailwind CSS Configuration
const tailwindConfig = {
  theme: {
    extend: {
      fontFamily: {
        sans: ["Work Sans", "sans-serif"],
        marker: ["Permanent Marker", "cursive"],
      },
      colors: {
        "pyxi-red": "#F09E5B",
      },
    },
  },
};

// Apply Tailwind config
if (typeof tailwind !== 'undefined') {
  tailwind.config = tailwindConfig;
}

// Make PyxiConfig globally available
window.PyxiConfig = PyxiConfig;